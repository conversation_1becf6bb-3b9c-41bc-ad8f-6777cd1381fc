"use client"

import { useEffect, useState } from "react"
import { useBlogBySlugWithStore } from "@/hooks"
import {
  Pagination,
  PaginationContent,
  PaginationItem,
  PaginationLink,
  PaginationNext,
  PaginationPrevious,
  PaginationEllipsis,
} from "@/components/ui/pagination"
import NewsItem from "./NewsItem"
import { cn } from "@/lib/utils"

interface BlogDetailPageProps {
  blogSlug: string
}

export default function BlogDetailPage({ blogSlug }: BlogDetailPageProps) {
  const { data: blog, isLoading, error } = useBlogBySlugWithStore(blogSlug)

  // Pagination state
  const [currentPage, setCurrentPage] = useState(1)
  const itemsPerPage = 3

  const BlogArticles = [
    {
      id: 1,
      title: "Welcome to Our Blog",
      slug: "welcome-to-our-blog",
      author_name: "Swaggold Team",
      short_description:
        "This is our first blog post introducing our new blog system.",
      feature_image:
        "https://admin.swaggold.co/storage/images/blogs/NK1t29APjpIugHSJGWnub5T1Ueenpjb4Ggye26yX.jpg",
      additional_images: [],
      images_count: 0,
      tags: ["welcome", "introduction", "blog"],
      published_at: "2025-06-13 18:06:48",
      status: "published",
      views_count: 15,
      reading_time: 1,
      excerpt:
        "Welcome to our new blog! We are excited to share our thoughts...",
      is_published: true,
      created_at: "2025-06-13 14:51:18",
      language: "en"
    },
    {
      id: 1,
      title: "Welcome to Our Blog",
      slug: "welcome-to-our-blog",
      author_name: "Swaggold Team",
      short_description:
        "This is our first blog post introducing our new blog system.",
      feature_image:
        "https://admin.swaggold.co/storage/images/blogs/NK1t29APjpIugHSJGWnub5T1Ueenpjb4Ggye26yX.jpg",
      additional_images: [],
      images_count: 0,
      tags: ["welcome", "introduction", "blog"],
      published_at: "2025-06-13 18:06:48",
      status: "published",
      views_count: 15,
      reading_time: 1,
      excerpt:
        "Welcome to our new blog! We are excited to share our thoughts...",
      is_published: true,
      created_at: "2025-06-13 14:51:18",
      language: "en"
    },
    {
      id: 1,
      title: "Welcome to Our Blog",
      slug: "welcome-to-our-blog",
      author_name: "Swaggold Team",
      short_description:
        "This is our first blog post introducing our new blog system.",
      feature_image:
        "https://admin.swaggold.co/storage/images/blogs/NK1t29APjpIugHSJGWnub5T1Ueenpjb4Ggye26yX.jpg",
      additional_images: [],
      images_count: 0,
      tags: ["welcome", "introduction", "blog"],
      published_at: "2025-06-13 18:06:48",
      status: "published",
      views_count: 15,
      reading_time: 1,
      excerpt:
        "Welcome to our new blog! We are excited to share our thoughts...",
      is_published: true,
      created_at: "2025-06-13 14:51:18",
      language: "en"
    }
  ]

  // Calculate pagination
  const totalPages = Math.ceil(BlogArticles.length / itemsPerPage)
  const startIndex = (currentPage - 1) * itemsPerPage
  const endIndex = startIndex + itemsPerPage
  const currentItems = BlogArticles.slice(startIndex, endIndex)

  const handlePageChange = (page: number) => {
    setCurrentPage(page)
    // Scroll to top of articles section
    document.getElementById('articles-section')?.scrollIntoView({
      behavior: 'smooth'
    })
  }

  return (
    <div className="max-w-5xl mx-auto px-4 py-8 text-gray-800">
      {/* Header */}
      <div className="flex items-center justify-between">
        <h1 className="text-6xl mb-4">
          {blog?.title || "Sample Article Title"}
        </h1>
        <p className="text-primary-500 flex items-center gap-2">
          <span className="text-primary-500 rounded-lg text-lg bg-primary-50 px-2">
            {"Business"}
          </span>
          <span className="text-primary-500 text-xl">•</span>
          <span className="text-secondary-400">{"October 20"}</span>
        </p>
      </div>

      {/* Blog Cover Image */}
      <div className="my-6">Content</div>
      <div id="articles-section" className="flex flex-col gap-6">
        {/* Suggested Articles */}
        <h2 className="text-5xl text-secondary-500">
          Articles You May Find Interesting
        </h2>
        <p className="text-primary text-md font-sukar">
          Explore carefully selected articles to help you gain a deeper
          understanding and practical application in the world of digital
          marketing.
        </p>

        {currentItems.map((item, index) => (
          <div
            key={`${item.id}-${startIndex + index}`}
            className={cn("flex items-start gap-4 mb-6 border-b pb-6",
              index === currentItems.length - 1 && "border-b-0 pb-0"
            )}
          >
            <NewsItem {...item} />
          </div>
        ))}
      </div>

      {/* Pagination */}
      {totalPages > 1 && (
        <Pagination className="mt-8">
          <PaginationContent>
            <PaginationItem>
              <PaginationPrevious
                href="#"
                onClick={(e) => {
                  e.preventDefault()
                  if (currentPage > 1) {
                    handlePageChange(currentPage - 1)
                  }
                }}
                className={currentPage === 1 ? "pointer-events-none opacity-50" : ""}
              />
            </PaginationItem>

            {/* Page Numbers */}
            {Array.from({ length: totalPages }, (_, i) => i + 1).map((page) => {
              // Show first page, last page, current page, and pages around current
              const showPage =
                page === 1 ||
                page === totalPages ||
                (page >= currentPage - 1 && page <= currentPage + 1)

              if (!showPage) {
                // Show ellipsis for gaps
                if (page === currentPage - 2 || page === currentPage + 2) {
                  return (
                    <PaginationItem key={`ellipsis-${page}`}>
                      <PaginationEllipsis />
                    </PaginationItem>
                  )
                }
                return null
              }

              return (
                <PaginationItem key={page}>
                  <PaginationLink
                    href="#"
                    onClick={(e) => {
                      e.preventDefault()
                      handlePageChange(page)
                    }}
                    isActive={currentPage === page}
                  >
                    {page}
                  </PaginationLink>
                </PaginationItem>
              )
            })}

            <PaginationItem>
              <PaginationNext
                href="#"
                onClick={(e) => {
                  e.preventDefault()
                  if (currentPage < totalPages) {
                    handlePageChange(currentPage + 1)
                  }
                }}
                className={currentPage === totalPages ? "pointer-events-none opacity-50" : ""}
              />
            </PaginationItem>
          </PaginationContent>
        </Pagination>
      )}
    </div>
  )
}
