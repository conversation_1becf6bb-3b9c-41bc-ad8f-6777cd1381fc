"use client"

import { useEffect, useState } from "react"

export default function EconomicTable() {
  interface CalendarItem {
    Country: string;
    Event: string;
    Actual: string | null;
    Forecast: string | null;
    Previous: string | null;
  }

  const [calendar, setCalendar] = useState<CalendarItem[]>([])

  useEffect(() => {
    fetch("/api/calendar")
      .then((res) => res.json())
      .then((data) => setCalendar(data))
  }, [])

  return (
    <table className="min-w-full border text-sm">
      <thead>
        <tr>
          <th className="border px-2 py-1">Country</th>
          <th className="border px-2 py-1">Event</th>
          <th className="border px-2 py-1">Actual</th>
          <th className="border px-2 py-1">Forecast</th>
          <th className="border px-2 py-1">Previous</th>
        </tr>
      </thead>
      <tbody>
        {calendar.slice(0, 20).map((item, i) => (
          <tr key={i}>
            <td className="border px-2 py-1">{item?.Country}</td>
            <td className="border px-2 py-1">{item?.Event}</td>
            <td className="border px-2 py-1">{item?.Actual}</td>
            <td className="border px-2 py-1">{item?.Forecast}</td>
            <td className="border px-2 py-1">{item.Previous}</td>
          </tr>
        ))}
      </tbody>
    </table>
  )
}
