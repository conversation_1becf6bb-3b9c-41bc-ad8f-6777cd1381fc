'use client';

import { useEffect } from 'react';
import { usePriceBySymbol, useLiveMarketInsightsStore } from '@/stores/liveMarketInsightsStore';

interface GoldPriceWidgetProps {
  symbol?: string;
}

export default function GoldPriceWidget({ symbol = 'GOLD' }: GoldPriceWidgetProps) {
  const goldPrice = usePriceBySymbol(symbol);
  const { fetchLiveMarketInsights } = useLiveMarketInsightsStore();

  useEffect(() => {
    // Only fetch when this component is used
    fetchLiveMarketInsights();
  }, [fetchLiveMarketInsights]);

  if (!goldPrice) {
    return (
      <div className="gold-price-widget bg-yellow-50 border border-yellow-200 rounded-lg p-4">
        <div className="animate-pulse">
          <div className="h-4 bg-yellow-200 rounded w-16 mb-2"></div>
          <div className="h-6 bg-yellow-200 rounded w-24"></div>
        </div>
      </div>
    );
  }

  return (
    <div className="gold-price-widget bg-gradient-to-r from-yellow-400 to-yellow-500 text-white rounded-lg p-4 shadow-lg">
      <div className="flex items-center justify-between">
        <div>
          <h3 className="text-sm font-medium opacity-90">{goldPrice.name}</h3>
          <p className="text-2xl font-bold">${goldPrice.current_price.toFixed(2)}</p>
        </div>
        <div className="text-right">
          <p className={`text-sm font-medium ${
            goldPrice.price_change_percentage_24h >= 0 ? 'text-green-100' : 'text-red-100'
          }`}>
            {goldPrice.price_change_percentage_24h >= 0 ? '↗' : '↘'} 
            {Math.abs(goldPrice.price_change_percentage_24h).toFixed(2)}%
          </p>
          <p className="text-xs opacity-75">24h change</p>
        </div>
      </div>
      
      {goldPrice.high_24h && goldPrice.low_24h && (
        <div className="mt-3 pt-3 border-t border-yellow-300 border-opacity-30">
          <div className="flex justify-between text-xs">
            <span>24h High: ${goldPrice.high_24h.toFixed(2)}</span>
            <span>24h Low: ${goldPrice.low_24h.toFixed(2)}</span>
          </div>
        </div>
      )}
    </div>
  );
}
