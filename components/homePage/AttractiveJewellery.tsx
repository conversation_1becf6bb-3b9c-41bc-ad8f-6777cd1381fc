"use client"

import { useTranslation } from "react-i18next"
import { JewelryCard } from "@/components/common/JewelryCard"
import { useCategoriesStore, useSystemSettingsStore } from "@/stores"

export function AttractiveJewellery() {
  const { t } = useTranslation()
  const { getSettingValue } = useSystemSettingsStore()
  const { categories } = useCategoriesStore()
  const collections = (categories || []).map((category) => {
    return {
      id: category.id,
      description: category.description,
      key: category.slug,
      title: category.name,
      image: category.image || ""
    }
  })

  const handleCollectionClick = (collectionKey: string) => {
    console.log(`Clicked on ${collectionKey} collection`)
    // Add navigation logic here
  }

  return (
    <section className="py-16">
      <div className="container mx-auto ">
        {/* Section Header */}
        <div className="text-center mb-12">
          {/* Section Title */}
          <p className="text-primary-500 text-lg uppercase tracking-wider mb-2">
            {getSettingValue("CATEGORY_SECTION_CAPTION") ||
              t("collections.section_title")}
          </p>

          {/* Main Title */}
          <h2 className="text-3xl md:text-6xl font-bold text-secondary-500 dark:text-white-500">
            {getSettingValue("CATEGORY_SECTION_HEADLINE") ||
              t("collections.section_title")}
          </h2>
        </div>

        {/* Collections Grid */}
        <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-5 gap-6 md:gap-8">
          {collections.map((collection) => (
            <JewelryCard
              key={collection.key}
              title={collection.title}
              image={collection.image}
              onClick={() => handleCollectionClick(collection.key)}
              className="w-full"
            />
          ))}
        </div>
      </div>
    </section>
  )
}
