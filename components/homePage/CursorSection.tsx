"use client"

import { ImageSlider } from "@/components/common/ImageSlider"
import { useSystemSettingsStore } from "@/stores"

export function CursorSection() {
  const { getSettingValue } = useSystemSettingsStore()
  const cursorImages = (getSettingValue("IMAGES_SECTION_LIST") || []).map(
    (image: string) => {
      return {
        src: image,
        alt: "Elegant jewelry collection showcase"
      }
    }
  )

  return (
    <section className="w-full">
      <ImageSlider
        images={cursorImages}
        autoplay={true}
        autoplayDelay={5000}
        className="w-full"
      />
    </section>
  )
}
