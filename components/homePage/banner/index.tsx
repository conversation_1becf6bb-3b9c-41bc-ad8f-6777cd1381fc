"use client"

import React from "react"
import { Icon } from "@/components/common/Icon"
import { useLanguageStore } from "@/stores/languageStore"
import { useSystemSetting, useSystemSettingsStore } from "@/stores"

export default function Banner() {
  const { isRTL } = useLanguageStore()
  const headline = useSystemSetting("HERO_BANNER_HEADLINE")
  const description = useSystemSetting("HERO_BANNER_DESCRIPTION")
  const ctaText = useSystemSetting("HERO_BANNER_CTA_TEXT")
  const ctaUrl = useSystemSetting("HERO_BANNER_CTA_URL")
  const videoUrl = useSystemSetting("HERO_BANNER_VIDEO_URL")

  const hero = {
    headline,
    description,
    ctaText,
    ctaUrl,
    videoUrl
  }

  // console.log({ settings })
  // const hero = settings.reduce((acc: Record<string, string>, item: any) => {
  //   if (item.key.startsWith("HERO_BANNER_")) {
  //     const key = item.key.replace("HERO_BANNER_", "").toLowerCase()
  //     acc[key] = item.value
  //   }
  //   return acc
  // }, {})

  // ✅ Always render consistent structure to prevent hydration mismatch
  const isLoading = !headline || !description || !ctaText || !ctaUrl || !videoUrl

  return (
    <div className="relative w-full h-screen overflow-hidden">
      {/* Background Video - only render when data is available */}
      {!isLoading && videoUrl && (
        <video
          className="absolute inset-0 w-full h-full object-cover"
          autoPlay
          muted
          loop
          playsInline
          poster="/images/jewelry-banner-poster.jpg"
        >
          <source src={videoUrl} type="video/mp4" />
        </video>
      )}

      {/* Fallback background for loading state */}
      {isLoading && (
        <div className="absolute inset-0 bg-gradient-to-br from-secondary-600 to-secondary-700" />
      )}

      {/* Animated background pattern as additional fallback */}
      <div className="absolute inset-0 opacity-20">
        <div className="absolute inset-0 bg-gradient-to-r from-transparent via-white/10 to-transparent animate-pulse" />
        <div className="absolute top-0 left-0 w-full h-full bg-[radial-gradient(circle_at_50%_50%,rgba(255,215,0,0.1),transparent_50%)]" />
      </div>

      {/* Dark overlay for better text readability */}
      <div className="absolute inset-0 bg-black/28" />

      {/* Content Container */}
      <div className="relative z-10 flex items-end justify-center h-full px-4 sm:px-6 lg:px-8">
        <div className="container">
          {/* Main Content Grid */}
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-8 lg:gap-12 mb-8 items-center min-h-[17.5vh]">
            {isLoading ? (
              // Loading skeleton
              <>
                <div className="space-y-4">
                  <div className="h-14 bg-white/10 rounded animate-pulse" />
                  <div className="h-14 bg-white/10 rounded animate-pulse w-3/4" />
                </div>
                <div className="space-y-4">
                  <div className="h-6 bg-white/10 rounded animate-pulse" />
                  <div className="h-6 bg-white/10 rounded animate-pulse w-5/6" />
                  <div className="h-6 bg-white/10 rounded animate-pulse w-4/6" />
                  <div className="h-10 bg-white/10 rounded animate-pulse w-32 mt-8" />
                </div>
              </>
            ) : (
              // Actual content
              <>
                <h1 className="justify-start text-primary-50 text-5xl font-normal leading-[56px]">
                  {headline}
                </h1>
                <div>
                  <p className="text-lg sm:text-xl text-primary-50 leading-relaxed mb-8">
                    {description}
                  </p>
                  <a href={ctaUrl} className="flex items-center gap-1">
                    <p className="text-lg sm:text-xl text-primary-300">{ctaText}</p>
                    <Icon
                      name={isRTL ? "arrow-left" : "arrow-right"}
                      size={20}
                      className="ml-2 text-primary-300"
                    />
                  </a>
                </div>
              </>
            )}
          </div>
        </div>
      </div>
    </div>
  )
}
