import { MainLayout } from '@/components/layout/MainLayout';
import { notFound } from 'next/navigation';
import { ReactNode } from 'react';

interface MarketInsightsLayoutProps {
  children: ReactNode;
  params: Promise<{
    locale: string;
  }>;
}

// Define supported locales
const supportedLocales = ['en', 'ar'];

export default async function LocalizedMarketInsightsLayout({ children, params }: MarketInsightsLayoutProps) {
  const { locale } = await params;

  // Validate locale
  if (!supportedLocales.includes(locale)) {
    notFound();
  }

  return (
    <MainLayout>
      {children}
    </MainLayout>
  );
}

// Generate static params for supported locales
export async function generateStaticParams() {
  return supportedLocales.map((locale) => ({
    locale,
  }));
}
