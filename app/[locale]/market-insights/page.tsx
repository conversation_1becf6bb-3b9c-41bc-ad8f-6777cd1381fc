import MarketInsights from '@/components/marketInsights';
import { notFound } from 'next/navigation';

interface MarketInsightsPageProps {
  params: Promise<{
    locale: string;
  }>;
}

// Define supported locales
const supportedLocales = ['en', 'ar'];

export default async function LocalizedMarketInsightsPage({ params }: MarketInsightsPageProps) {
  const { locale } = await params;

  // Validate locale
  if (!supportedLocales.includes(locale)) {
    notFound();
  }

  return <MarketInsights />;
}

// Generate static params for supported locales
export async function generateStaticParams() {
  return supportedLocales.map((locale) => ({
    locale,
  }));
}
