import { create } from 'zustand';
import { persist } from 'zustand/middleware';
import { 
  LiveMarketData, 
  MarketPrice,
  MarketNews,
  MarketIndicator
} from '@/types/api';

interface LiveMarketInsightsStore {
  marketData: LiveMarketData | null;
  prices: MarketPrice[];
  news: MarketNews[];
  indicators: MarketIndicator[];
  setMarketData: (data: LiveMarketData | null) => void;
  setPrices: (prices: MarketPrice[]) => void;
  setNews: (news: MarketNews[]) => void;
  setIndicators: (indicators: MarketIndicator[]) => void;
  getPriceBySymbol: (symbol: string) => MarketPrice | undefined;
  getNewsByCategory: (category: string) => MarketNews[];
  getIndicatorsByCategory: (category: string) => MarketIndicator[];
}

export const useLiveMarketInsightsStoreRQ = create<LiveMarketInsightsStore>()(
  persist(
    (set, get) => ({
      // Initial state
      marketData: null,
      prices: [],
      news: [],
      indicators: [],

      // Set market data from React Query
      setMarketData: (marketData: LiveMarketData | null) => {
        set({ 
          marketData,
          prices: marketData?.prices || [],
          news: marketData?.news || [],
          indicators: marketData?.indicators || [],
        });
      },

      // Set prices
      setPrices: (prices: MarketPrice[]) => {
        set({ prices });
      },

      // Set news
      setNews: (news: MarketNews[]) => {
        set({ news });
      },

      // Set indicators
      setIndicators: (indicators: MarketIndicator[]) => {
        set({ indicators });
      },

      // Get price by symbol
      getPriceBySymbol: (symbol: string) => {
        return get().prices.find(price => price.symbol === symbol);
      },

      // Get news by category
      getNewsByCategory: (category: string) => {
        return get().news.filter(item => item.category === category);
      },

      // Get indicators by category
      getIndicatorsByCategory: (category: string) => {
        return get().indicators.filter(item => item.category === category);
      },
    }),
    {
      name: 'live-market-insights-storage-rq',
      // Only persist some data, not all live data
      partialize: (state) => ({
        marketData: state.marketData,
        prices: state.prices,
        news: state.news,
        indicators: state.indicators,
      }),
    }
  )
);

// Selector hooks for common use cases
export const useMarketPricesRQ = () => {
  return useLiveMarketInsightsStoreRQ((state) => state.prices);
};

export const useMarketNewsRQ = () => {
  return useLiveMarketInsightsStoreRQ((state) => state.news);
};

export const useMarketIndicatorsRQ = () => {
  return useLiveMarketInsightsStoreRQ((state) => state.indicators);
};

export const useLiveMarketDataRQ = () => {
  return useLiveMarketInsightsStoreRQ((state) => state.marketData);
};

export const usePriceBySymbolRQ = (symbol: string) => {
  return useLiveMarketInsightsStoreRQ((state) => state.getPriceBySymbol(symbol));
};
