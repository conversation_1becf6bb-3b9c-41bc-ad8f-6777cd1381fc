import { create } from 'zustand';
import { persist } from 'zustand/middleware';
import { apiClient, apiEndpoints, apiRequest } from '@/lib/api';
import { 
  LiveMarketData, 
  LiveMarketInsightsResponse, 
  LiveMarketInsightsState,
  MarketPrice,
  MarketNews,
  MarketIndicator
} from '@/types/api';

interface LiveMarketInsightsStore extends LiveMarketInsightsState {
  fetchLiveMarketInsights: () => Promise<void>;
  refreshLiveMarketInsights: () => Promise<void>;
  startAutoRefresh: () => void;
  stopAutoRefresh: () => void;
  setRefreshInterval: (interval: number) => void;
  getPriceBySymbol: (symbol: string) => MarketPrice | undefined;
  getNewsByCategory: (category: string) => MarketNews[];
  getIndicatorsByCategory: (category: string) => MarketIndicator[];
  isLoading: () => boolean;
  hasError: () => boolean;
  clearError: () => void;
}

export const useLiveMarketInsightsStore = create<LiveMarketInsightsStore>()(
  persist(
    (set, get) => ({
      // Initial state
      marketData: null,
      prices: [],
      news: [],
      indicators: [],
      autoRefresh: false,
      refreshInterval: 30000, // 30 seconds default
      loading: false,
      error: null,
      lastFetch: null,

      // Fetch live market insights from API
      fetchLiveMarketInsights: async () => {
        const state = get();
        
        // Avoid duplicate requests if already loading
        if (state.loading) return;

        // Check if we have recent data (cache for 1 minute for live data)
        const now = Date.now();
        const cacheTime = 60 * 1000; // 1 minute
        if (state.lastFetch && (now - state.lastFetch) < cacheTime && state.marketData) {
          return;
        }

        set({ loading: true, error: null });

        try {
          const response = await apiRequest<LiveMarketInsightsResponse>(() =>
            apiClient.get(apiEndpoints.liveMarketInsights)
          );

          const marketData = response.data;

          set({
            marketData,
            prices: marketData.prices || [],
            news: marketData.news || [],
            indicators: marketData.indicators || [],
            loading: false,
            error: null,
            lastFetch: now,
          });
        } catch (error) {
          const errorMessage = error instanceof Error ? error.message : 'Failed to fetch live market insights';
          set({
            loading: false,
            error: errorMessage,
          });
          console.error('Error fetching live market insights:', error);
        }
      },

      // Force refresh live market insights (ignores cache)
      refreshLiveMarketInsights: async () => {
        set({ lastFetch: null });
        await get().fetchLiveMarketInsights();
      },

      // Start auto refresh
      startAutoRefresh: () => {
        const state = get();
        if (state.autoRefresh) return; // Already running

        set({ autoRefresh: true });

        const intervalId = setInterval(() => {
          const currentState = get();
          if (currentState.autoRefresh) {
            currentState.refreshLiveMarketInsights();
          } else {
            clearInterval(intervalId);
          }
        }, state.refreshInterval);

        // Store interval ID for cleanup
        (get() as any).intervalId = intervalId;
      },

      // Stop auto refresh
      stopAutoRefresh: () => {
        const state = get() as any;
        if (state.intervalId) {
          clearInterval(state.intervalId);
          delete state.intervalId;
        }
        set({ autoRefresh: false });
      },

      // Set refresh interval
      setRefreshInterval: (interval: number) => {
        set({ refreshInterval: interval });
        
        // Restart auto refresh with new interval if it's currently running
        const state = get();
        if (state.autoRefresh) {
          state.stopAutoRefresh();
          setTimeout(() => state.startAutoRefresh(), 100);
        }
      },

      // Get price by symbol
      getPriceBySymbol: (symbol: string) => {
        return get().prices.find(price => price.symbol.toLowerCase() === symbol.toLowerCase());
      },

      // Get news by category
      getNewsByCategory: (category: string) => {
        return get().news.filter(newsItem => 
          newsItem.category.toLowerCase() === category.toLowerCase()
        );
      },

      // Get indicators by category
      getIndicatorsByCategory: (category: string) => {
        return get().indicators.filter(indicator => 
          indicator.category.toLowerCase() === category.toLowerCase()
        );
      },

      // Helper methods
      isLoading: () => get().loading,
      hasError: () => !!get().error,
      clearError: () => set({ error: null }),
    }),
    {
      name: 'live-market-insights-storage',
      // Only persist some data, not loading states or auto-refresh state
      partialize: (state) => ({
        marketData: state.marketData,
        prices: state.prices,
        news: state.news,
        indicators: state.indicators,
        refreshInterval: state.refreshInterval,
        lastFetch: state.lastFetch,
      }),
      // Don't auto-fetch on rehydration - only fetch when explicitly called
      onRehydrateStorage: () => (state) => {
        // Just rehydrate the state, don't auto-fetch
        return state;
      },
    }
  )
);

// Selector hooks for common use cases
export const useMarketPrices = () => {
  return useLiveMarketInsightsStore((state) => state.prices);
};

export const useMarketNews = () => {
  return useLiveMarketInsightsStore((state) => state.news);
};

export const useMarketIndicators = () => {
  return useLiveMarketInsightsStore((state) => state.indicators);
};

export const useLiveMarketData = () => {
  return useLiveMarketInsightsStore((state) => state.marketData);
};

export const useMarketInsightsLoading = () => {
  return useLiveMarketInsightsStore((state) => state.loading);
};

export const useMarketInsightsError = () => {
  return useLiveMarketInsightsStore((state) => state.error);
};

export const useAutoRefreshState = () => {
  return useLiveMarketInsightsStore((state) => ({
    autoRefresh: state.autoRefresh,
    refreshInterval: state.refreshInterval,
  }));
};

export const usePriceBySymbol = (symbol: string) => {
  return useLiveMarketInsightsStore((state) => state.getPriceBySymbol(symbol));
};

export const useNewsByCategory = (category: string) => {
  return useLiveMarketInsightsStore((state) => state.getNewsByCategory(category));
};

export const useIndicatorsByCategory = (category: string) => {
  return useLiveMarketInsightsStore((state) => state.getIndicatorsByCategory(category));
};
