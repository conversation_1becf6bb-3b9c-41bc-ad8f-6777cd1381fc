'use client'

import { useEffect } from 'react'
import { useLiveMarketInsights, useMarketPrices, useMarketNews, useMarketIndicators } from '@/hooks/api'
import { useLiveMarketInsightsStoreRQ } from '@/stores/liveMarketInsightsStoreRQ'

export const useLiveMarketInsightsWithStore = () => {
  const { setMarketData } = useLiveMarketInsightsStoreRQ()
  
  const query = useLiveMarketInsights()
  
  // Update Zustand store when React Query data changes
  useEffect(() => {
    if (query.data) {
      setMarketData(query.data)
    }
  }, [query.data, setMarketData])
  
  return {
    ...query,
    // Expose store methods for backward compatibility
    getPriceBySymbol: useLiveMarketInsightsStoreRQ(state => state.getPriceBySymbol),
    getNewsByCategory: useLiveMarketInsightsStoreRQ(state => state.getNewsByCategory),
    getIndicatorsByCategory: useLiveMarketInsightsStoreRQ(state => state.getIndicatorsByCategory),
  }
}

export const useMarketPricesWithStore = () => {
  const { setPrices } = useLiveMarketInsightsStoreRQ()
  
  const query = useMarketPrices()
  
  // Update Zustand store when React Query data changes
  useEffect(() => {
    if (query.data) {
      setPrices(query.data)
    }
  }, [query.data, setPrices])
  
  return query
}

export const useMarketNewsWithStore = () => {
  const { setNews } = useLiveMarketInsightsStoreRQ()
  
  const query = useMarketNews()
  
  // Update Zustand store when React Query data changes
  useEffect(() => {
    if (query.data) {
      setNews(query.data)
    }
  }, [query.data, setNews])
  
  return query
}

export const useMarketIndicatorsWithStore = () => {
  const { setIndicators } = useLiveMarketInsightsStoreRQ()
  
  const query = useMarketIndicators()
  
  // Update Zustand store when React Query data changes
  useEffect(() => {
    if (query.data) {
      setIndicators(query.data)
    }
  }, [query.data, setIndicators])
  
  return query
}
