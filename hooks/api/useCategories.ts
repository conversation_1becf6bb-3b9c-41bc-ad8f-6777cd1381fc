import { useQuery, UseQueryOptions } from '@tanstack/react-query'
import { apiClient, apiEndpoints, apiRequest } from '@/lib/api'
import { Category, CategoriesResponse } from '@/types/api'

export const useCategories = (
  options?: Omit<UseQueryOptions<Category[], Error>, 'queryKey' | 'queryFn'>
) => {
  return useQuery({
    queryKey: ['categories'],
    queryFn: async () => {
      const response = await apiRequest<CategoriesResponse>(() =>
        apiClient.get(apiEndpoints.categories)
      )
      return response.data?.items || []
    },
    staleTime: 15 * 60 * 1000, // 15 minutes for categories
    ...options,
  })
}

export const useActiveCategories = (
  options?: Omit<UseQueryOptions<Category[], Error>, 'queryKey' | 'queryFn'>
) => {
  return useQuery({
    queryKey: ['categories', 'active'],
    queryFn: async () => {
      const response = await apiRequest<CategoriesResponse>(() =>
        apiClient.get(apiEndpoints.categories)
      )
      const categories = response.data?.items || []
      return categories.filter(category => category.is_active)
    },
    staleTime: 15 * 60 * 1000, // 15 minutes for categories
    ...options,
  })
}

export const useParentCategories = (
  options?: Omit<UseQueryOptions<Category[], Error>, 'queryKey' | 'queryFn'>
) => {
  return useQuery({
    queryKey: ['categories', 'parents'],
    queryFn: async () => {
      const response = await apiRequest<CategoriesResponse>(() =>
        apiClient.get(apiEndpoints.categories)
      )
      const categories = response.data?.items || []
      return categories.filter(category => !category.parent_id)
    },
    staleTime: 15 * 60 * 1000, // 15 minutes for categories
    ...options,
  })
}

export const useCategoryBySlug = (
  slug: string,
  options?: Omit<UseQueryOptions<Category | undefined, Error>, 'queryKey' | 'queryFn'>
) => {
  return useQuery({
    queryKey: ['category', slug],
    queryFn: async () => {
      const response = await apiRequest<CategoriesResponse>(() =>
        apiClient.get(apiEndpoints.categories)
      )
      const categories = response.data?.items || []
      return categories.find(category => category.slug === slug)
    },
    staleTime: 15 * 60 * 1000, // 15 minutes for categories
    enabled: !!slug,
    ...options,
  })
}

export const useCategoryChildren = (
  parentId: number,
  options?: Omit<UseQueryOptions<Category[], Error>, 'queryKey' | 'queryFn'>
) => {
  return useQuery({
    queryKey: ['categories', 'children', parentId],
    queryFn: async () => {
      const response = await apiRequest<CategoriesResponse>(() =>
        apiClient.get(apiEndpoints.categories)
      )
      const categories = response.data?.items || []
      return categories.filter(category => category.parent_id === parentId)
    },
    staleTime: 15 * 60 * 1000, // 15 minutes for categories
    enabled: !!parentId,
    ...options,
  })
}
