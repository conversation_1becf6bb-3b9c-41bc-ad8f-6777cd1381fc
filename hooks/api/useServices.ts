import { useQuery, UseQueryOptions } from '@tanstack/react-query'
import { apiClient, apiEndpoints, apiRequest } from '@/lib/api'
import { Service, ServicesResponse } from '@/types/api'

export const useServices = (
  options?: Omit<UseQueryOptions<Service[], Error>, 'queryKey' | 'queryFn'>
) => {
  return useQuery({
    queryKey: ['services'],
    queryFn: async () => {
      const response = await apiRequest<ServicesResponse>(() =>
        apiClient.get(apiEndpoints.services)
      )
      return response.data?.items || []
    },
    staleTime: 10 * 60 * 1000, // 10 minutes for services
    ...options,
  })
}

export const useActiveServices = (
  options?: Omit<UseQueryOptions<Service[], Error>, 'queryKey' | 'queryFn'>
) => {
  return useQuery({
    queryKey: ['services', 'active'],
    queryFn: async () => {
      const response = await apiRequest<ServicesResponse>(() =>
        apiClient.get(apiEndpoints.services)
      )
      const services = response.data?.items || []
      return services.filter(service => service.is_active)
    },
    staleTime: 10 * 60 * 1000, // 10 minutes for services
    ...options,
  })
}

export const useServiceBySlug = (
  slug: string,
  options?: Omit<UseQueryOptions<Service | undefined, Error>, 'queryKey' | 'queryFn'>
) => {
  return useQuery({
    queryKey: ['service', slug],
    queryFn: async () => {
      const response = await apiRequest<ServicesResponse>(() =>
        apiClient.get(apiEndpoints.services)
      )
      const services = response.data?.items || []
      return services.find(service => service.slug === slug)
    },
    staleTime: 10 * 60 * 1000, // 10 minutes for services
    enabled: !!slug,
    ...options,
  })
}
