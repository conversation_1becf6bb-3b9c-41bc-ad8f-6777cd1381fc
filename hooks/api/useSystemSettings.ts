import { useQuery, UseQueryOptions } from '@tanstack/react-query'
import { apiClient, apiEndpoints, apiRequest } from '@/lib/api'
import { SystemSetting, SystemSettingsResponse } from '@/types/api'

export interface UseSystemSettingsOptions {
  visibleOnly?: boolean
  keyFilter?: string
  enabled?: boolean
}

export const useSystemSettings = (
  options: UseSystemSettingsOptions = {},
  queryOptions?: Omit<UseQueryOptions<SystemSetting[], Error>, 'queryKey' | 'queryFn'>
) => {
  const { visibleOnly = true, keyFilter, enabled = true } = options

  return useQuery({
    queryKey: ['systemSettings', { visibleOnly, keyFilter }],
    queryFn: async () => {
      const params = new URLSearchParams()
      if (visibleOnly) params.append('visible_only', '1')
      if (keyFilter) params.append('key_filter', keyFilter)

      const response = await apiRequest<SystemSettingsResponse>(() =>
        apiClient.get(`${apiEndpoints.systemSettings}?${params.toString()}`)
      )
      return response.data
    },
    enabled,
    staleTime: 10 * 60 * 1000, // 10 minutes for system settings
    ...queryOptions,
  })
}

export const useSystemSettingByKey = (
  key: string,
  options?: Omit<UseQueryOptions<SystemSetting | undefined, Error>, 'queryKey' | 'queryFn'>
) => {
  return useQuery({
    queryKey: ['systemSetting', key],
    queryFn: async () => {
      const response = await apiRequest<SystemSettingsResponse>(() =>
        apiClient.get(`${apiEndpoints.systemSettings}?key_filter=${key}`)
      )
      return response.data.find(setting => setting.key === key)
    },
    staleTime: 10 * 60 * 1000, // 10 minutes for system settings
    ...options,
  })
}
