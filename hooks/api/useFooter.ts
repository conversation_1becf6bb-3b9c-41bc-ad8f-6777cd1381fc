import { useQuery, UseQueryOptions } from '@tanstack/react-query'
import { apiClient, apiEndpoints, apiRequest } from '@/lib/api'
import { FooterData, FooterResponse } from '@/types/api'

export const useFooter = (
  options?: Omit<UseQueryOptions<FooterData, Error>, 'queryKey' | 'queryFn'>
) => {
  return useQuery({
    queryKey: ['footer'],
    queryFn: async () => {
      const response = await apiRequest<FooterResponse>(() =>
        apiClient.get(apiEndpoints.footer)
      )
      return response.data
    },
    staleTime: 15 * 60 * 1000, // 15 minutes for footer data
    ...options,
  })
}
