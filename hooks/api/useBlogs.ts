import { useQuery, useInfiniteQuery, UseQueryOptions, UseInfiniteQueryOptions } from '@tanstack/react-query'
import { apiClient, apiEndpoints, apiRequest } from '@/lib/api'
import { Blog, BlogsResponse, BlogDetailResponse, BlogsParams } from '@/types/api'

export const useBlogs = (
  params: BlogsParams = {},
  options?: Omit<UseQueryOptions<any, Error>, 'queryKey' | 'queryFn'>
) => {
  return useQuery({
    queryKey: ['blogs', params],
    queryFn: async () => {
      const queryParams = new URLSearchParams()
      Object.entries(params).forEach(([key, value]) => {
        if (value !== undefined && value !== null) {
          queryParams.append(key, value.toString())
        }
      })

      const response = await apiRequest<any>(() =>
        apiClient.get(`${apiEndpoints.blogs}?${queryParams.toString()}`)
      )
      return response
    },
    staleTime: 5 * 60 * 1000, // 5 minutes for blogs
    ...options,
  })
}

export const useBlogsPaginated = (
  params: BlogsParams = {},
  options?: Omit<UseInfiniteQueryOptions<BlogsResponse, Error>, 'queryKey' | 'queryFn' | 'getNextPageParam' | 'initialPageParam'>
) => {
  return useInfiniteQuery({
    queryKey: ['blogs', 'paginated', params],
    queryFn: async ({ pageParam = 1 }) => {
      const queryParams = new URLSearchParams()
      Object.entries({ ...params, page: pageParam }).forEach(([key, value]) => {
        if (value !== undefined && value !== null) {
          queryParams.append(key, value.toString())
        }
      })

      const response = await apiRequest<BlogsResponse>(() =>
        apiClient.get(`${apiEndpoints.blogs}?${queryParams.toString()}`)
      )
      return response
    },
    getNextPageParam: (lastPage) => {
      if (lastPage.current_page < lastPage.last_page) {
        return lastPage.current_page + 1
      }
      return undefined
    },
    initialPageParam: 1,
    staleTime: 5 * 60 * 1000, // 5 minutes for blogs
    ...options,
  })
}

export const useBlogBySlug = (
  slug: string,
  options?: Omit<UseQueryOptions<Blog, Error>, 'queryKey' | 'queryFn'>
) => {
  return useQuery({
    queryKey: ['blog', slug],
    queryFn: async () => {
      const response = await apiRequest<BlogDetailResponse>(() =>
        apiClient.get(apiEndpoints.blogDetail(slug))
      )
      return response.data
    },
    staleTime: 10 * 60 * 1000, // 10 minutes for individual blog posts
    enabled: !!slug,
    ...options,
  })
}

