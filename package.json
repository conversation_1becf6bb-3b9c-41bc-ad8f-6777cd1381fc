{"name": "swag", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev --turbopack --port 3005", "build": "next build", "start": "next start", "lint": "next lint"}, "dependencies": {"@radix-ui/react-dialog": "^1.1.14", "@radix-ui/react-dropdown-menu": "^2.1.15", "@radix-ui/react-label": "^2.1.7", "@radix-ui/react-slot": "^1.2.3", "@radix-ui/react-tabs": "^1.1.12", "@tanstack/react-query": "^5.81.0", "@tanstack/react-query-devtools": "^5.81.0", "axios": "^1.10.0", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "embla-carousel-autoplay": "^8.6.0", "embla-carousel-react": "^8.6.0", "gsap": "^3.13.0", "i18next": "^25.2.1", "i18next-browser-languagedetector": "^8.1.0", "lucide-react": "^0.511.0", "next": "15.3.2", "react": "^19.0.0", "react-dom": "^19.0.0", "react-i18next": "^15.5.2", "react-icons": "^5.5.0", "rss-parser": "^3.13.0", "socket.io-client": "^4.8.1", "tailwind-merge": "^3.3.0", "tailwindcss-animate": "^1.0.7", "tailwindcss-rtl": "^0.9.0", "zustand": "^5.0.5"}, "devDependencies": {"@types/node": "^20", "@types/react": "^19", "@types/react-dom": "^19", "autoprefixer": "^10.4.21", "postcss": "^8.5.3", "tailwindcss": "^3.4.17", "typescript": "^5"}}